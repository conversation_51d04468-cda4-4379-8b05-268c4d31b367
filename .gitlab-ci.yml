variables:
  USER: "gxxj"
  MEMORY_SIZE: "2g"
  STAGE_SERVERS: "************"
  PROD_SERVERS: "**************"
  DEPLOY_PATH: "${CI_PROJECT_PATH}"
  UPLOAD_PATH: "${CI_PROJECT_PATH}/file/images"

# stages:
#   - build
#   - deploy
#   - scan
#   - issue

.build_template: &build_template
  stage: build
  before_script:
    - echo "Mvn $(mvn -version)"
  artifacts:
    expire_in: 1 day
    paths:
      - target/*.jar

build staging package:
  <<: *build_template
  tags:
    - dev-backend-03
  only:
    - master
    - main
  script:
    - mvn clean package -P staging

deploy to staging server:
  stage: deploy
  tags:
    - dev-backend-03
  only:
    - master
    - main
  script:
    - |
      for SERVER in $STAGE_SERVERS; do
        echo "---> Server: $SERVER"
        ssh "${USER}@${SERVER}" "mkdir -p ${UPLOAD_PATH} ${DEPLOY_PATH} && cd ${DEPLOY_PATH} && pwd && exit"
        rsync -avzq --delete target/*.jar up.sh "${USER}@${SERVER}:${DEPLOY_PATH}"
        ssh "${USER}@${SERVER}" "bash -l -c 'cd ${DEPLOY_PATH} && chmod u+x ./up.sh && ./up.sh'"
        sleep 15
      done

build production package:
  <<: *build_template
  tags:
    - edu-backend-03
  only:
    - release
    - production
  script:
    - mvn clean package -P production

deploy to production server:
  stage: deploy
  tags:
    - edu-backend-03
  only:
    - release
    - production
  script:
    - |
      for SERVER in $PROD_SERVERS; do
        echo "---> Server: $SERVER"
        ssh "${USER}@${SERVER}" "mkdir -p ${UPLOAD_PATH} ${DEPLOY_PATH} && cd ${DEPLOY_PATH} && pwd && exit"
        rsync -avzq --delete target/*.jar up.sh "${USER}@${SERVER}:${DEPLOY_PATH}"
        ssh "${USER}@${SERVER}" "bash -l -c 'cd ${DEPLOY_PATH} && chmod u+x ./up.sh && ./up.sh -m ${MEMORY_SIZE}'"
        sleep 15
      done
