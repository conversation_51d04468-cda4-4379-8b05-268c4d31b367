# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 通用指南

*   始终使用中文回复。

## Commands

### Building the project

To build the project, run the following <PERSON>ven command:

```bash
mvn clean install
```

### Running the application

You can run the application in a few ways:

1.  **Directly from your IDE**: Find and run the `WxMpDemoApplication` main class.
2.  **Using the startup script**: The `up.sh` script can be used to run the packaged application JAR. It handles stopping old instances and cleaning up old JARs.

    ```bash
    ./up.sh -n wechat-message-push -m 512m
    ```

    *   `-n` specifies the application name (prefix for the JAR file).
    *   `-m` specifies the memory allocation.

### Publishing

The `package.json` file contains scripts for publishing to different environments:

*   `npm run publish:staging`: Pushes the `dev` branch to `master`.
*   `npm run publish:production`: Pushes the `dev` branch to `release`.
*   `npm run publish:all`: Runs both staging and production publishing.

## Code Architecture

This is a Spring Boot application that integrates with the WeChat Official Accounts Platform using the `WxJava` library.

### Configuration

*   Application configuration is managed in `src/main/resources/application.yml`. This file should be created by copying or renaming the `src/main/resources/application.yml.template` file.
*   The configuration includes WeChat AppID, secret, token, and an optional Redis configuration. The application supports multi-account configuration.

### Core Logic

*   The main application entry point is `com.github.binarywang.demo.wx.mp.WxMpDemoApplication`.
*   The core business logic should be implemented by creating or modifying handlers in the `com.github.binarywang.demo.wx.mp.handler` package. These handlers process different types of WeChat messages and events.
*   The existing handlers provide a basic structure for handling messages, subscriptions, and other events. You can extend these to add custom functionality.
