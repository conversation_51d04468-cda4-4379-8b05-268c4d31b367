# Docker Compose 版本
version: "3.8"

# 定义服务
services:
  # 应用服务
  wechat-app:
    # 直接使用由 'mvn docker:build' 构建的镜像
    image: wechat-mp/wechat-message-push
    container_name: wechat-app
    ports:
      - "8021:8021"
    depends_on:
      - redis
    environment:
      - WX_MP_USE_REDIS=true
    networks:
      - wechat-net

  redis:
    image: "redis:alpine"
    container_name: redis
    networks:
      - wechat-net

networks:
  wechat-net:
    driver: bridge
