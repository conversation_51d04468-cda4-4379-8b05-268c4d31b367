package com.github.binarywang.demo.wx.mp.controller;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import static me.chanjar.weixin.mp.enums.WxMpApiUrl.TemplateMsg.MESSAGE_TEMPLATE_SEND;

@AllArgsConstructor
@RestController
@Tag(name = "模板消息")
@RequestMapping("/api/wx/{appid}/template")
public class WxTemplateMsgController {
    private final WxMpService wxService;

    @Operation(summary = "获取已选用模板列表", description = "已添加至账号下所有模板列表", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/notify/template/api_getalltemplates.html"))
    @GetMapping("")
    public List<WxMpTemplate> getAllTemplates(@PathVariable String appid) throws WxErrorException {
        return wxService.getTemplateMsgService().getAllPrivateTemplate();
    }

    /**
     * {
     * "toUser": "oerj70bWKMIOuDdv7bl-Islb3q44",
     * "templateId": "n1bKkOIP2aNbEzrD1YVcOm62Gsm0PflRfMNgattRFJc",
     * "url":
     * "https://wfedu.wfgxic.com/result/93f47f82-9b55-407f-a09c-6c3043dd78eb?code=370711",
     * "data": [
     * { "name":"keyword1", "value": "张立家"},
     * { "name":"keyword2", "value": "370725199507123456"},
     * { "name":"keyword3", "value": "123456"},
     * { "name":"keyword4", "value": "潍坊行知学校"},
     * { "name":"keyword5", "value": "特殊才能"}
     * ]
     * }
     * // templateMessage.setToUser("oerj70bWKMIOuDdv7bl-Islb3q44");
     * //
     * templateMessage.setTemplateId("n1bKkOIP2aNbEzrD1YVcOm62Gsm0PflRfMNgattRFJc");
     * //
     * templateMessage.setUrl("https://wfedu.wfgxic.com/result/93f47f82-9b55-407f-a09c-6c3043dd78eb?code=370711");
     * // templateMessage.addData(new WxMpTemplateData("keyword1", "张立家"));
     * // templateMessage.addData(new WxMpTemplateData("keyword2",
     * "370725199507123456"));
     * // templateMessage.addData(new WxMpTemplateData("keyword3",
     * "370725199507123456"));
     * // templateMessage.addData(new WxMpTemplateData("keyword4", "潍坊行知学校"));
     * // templateMessage.addData(new WxMpTemplateData("keyword5", "特殊才能"));
     *
     */
    @Operation(summary = "发送模板消息", description = "通用模板消息推送接口", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/notify/template/api_sendtemplatemessage.html"))
    @PostMapping("/send")
    public String sendTemplateMsg(@PathVariable String appid, @RequestBody WxMpTemplateMessage templateMessage)
            throws WxErrorException {
        return wxService.post(MESSAGE_TEMPLATE_SEND, templateMessage.toJson());
    }
}
