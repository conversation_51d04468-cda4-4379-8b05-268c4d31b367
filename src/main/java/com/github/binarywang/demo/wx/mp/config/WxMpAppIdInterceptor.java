package com.github.binarywang.demo.wx.mp.config;

import lombok.AllArgsConstructor;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

import static org.springframework.web.servlet.HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE;

@Component
@AllArgsConstructor
public class WxMpAppIdInterceptor implements HandlerInterceptor {
    private final WxMpService wxService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        final Object attribute = request.getAttribute(URI_TEMPLATE_VARIABLES_ATTRIBUTE);
        if (attribute instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, String> uriTemplateVariables = (Map<String, String>) attribute;
            String appid = uriTemplateVariables.get("appid");
            if (appid == null) {
                return true;
            }
            if (!this.wxService.switchover(appid)) {
                throw new IllegalArgumentException(String.format("无效的 AppID: %s 请检查配置信息", appid));
            }
        }
        return true;
    }
}
