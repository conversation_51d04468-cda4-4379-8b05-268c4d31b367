package com.github.binarywang.demo.wx.mp.controller;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.web.bind.annotation.*;

@AllArgsConstructor
@Tag(name = "个性化菜单")
@RestController
@RequestMapping("/api/wx/{appid}/conditionalMenu")
public class WxConditionalMenuController {
    private final WxMpService wxService;

    @Operation(summary = "创建个性化菜单", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/custommenu/api_addconditionalmenu.html"))
    @PostMapping
    public String addConditionalMenu(@PathVariable String appid, @RequestBody WxMenu menu) throws WxErrorException {
        return this.wxService.switchoverTo(appid).getMenuService().menuCreate(menu);
    }

    /**
     * 删除个性化菜单接口
     *
     * @param menuId 个性化菜单的 menuid
     */
    @Operation(summary = "删除个性化菜单", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/custommenu/api_deleteconditionalmenu.html"))
    @DeleteMapping("/{menuId}")
    public void menuDelete(@PathVariable String appid, @PathVariable String menuId) throws WxErrorException {
        this.wxService.switchoverTo(appid).getMenuService().menuDelete(menuId);
    }

    /**
     * 测试个性化菜单匹配结果
     *
     * @param userid 可以是粉丝的 OpenID，也可以是粉丝的微信号。
     */
    @Operation(summary = "测试个性化菜单匹配结果", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/custommenu/api_trymatchmenu.html"))
    @GetMapping("/menuTryMatch/{userid}")
    public WxMenu menuTryMatch(@PathVariable String appid, @PathVariable String userid) throws WxErrorException {
        return this.wxService.switchoverTo(appid).getMenuService().menuTryMatch(userid);
    }
}
