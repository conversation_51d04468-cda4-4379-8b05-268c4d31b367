package com.github.binarywang.demo.wx.mp.controller;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RestController
@Tag(name = "基础接口")
@RequestMapping("/api/wx/{appid}/base")
public class WxBaseController {
    private final WxMpService wxService;

    @Operation(summary = "获取公众号 accessToken", description = "获取微信公众号的全局唯一接口调用凭据", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/base/api_getaccesstoken.html"))
    @GetMapping("/token")
    public String getToken(@PathVariable String appid) {
        try {
            String accessToken = wxService.getAccessToken();
            return accessToken;
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return null;
    }
}
