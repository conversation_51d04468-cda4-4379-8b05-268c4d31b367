package com.github.binarywang.demo.wx.mp.controller;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

@AllArgsConstructor
@RestController
@Tag(name = "网页开发 JSSDK")
@RequestMapping("/api/wx/{appid}/jssdk")
public class WxSdkController {
    private final WxMpService wxService;

    @Operation(summary = "获取 JSSDK 的 JSAPI Ticket", description = "获取 JSSDK 的 JSAPI Ticket", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/guide/h5/jssdk.html"))
    @GetMapping("/jsapiTicket")
    public String getJsapiTicket(@PathVariable String appid) throws WxErrorException {
        final WxJsapiSignature jsapiSignature = this.wxService.switchoverTo(appid).createJsapiSignature("111");
        System.out.println(jsapiSignature);
        return this.wxService.getJsapiTicket(true);
    }

    @Operation(summary = "网页授权获取用户信息", description = "通过 code 换取网页授权 access_token，并获取用户信息", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/guide/h5/auth.html"))
    @GetMapping("/greet")
    public String greetUser(@PathVariable String appid, @RequestParam String code, ModelMap map) {
        try {
            WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);
            WxOAuth2UserInfo user = wxService.getOAuth2Service().getUserInfo(accessToken, null);
            map.put("user", user);
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return "greet_user";
    }

}
