package com.github.binarywang.demo.wx.mp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import lombok.extern.slf4j.Slf4j;

import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.Enumeration;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
@SpringBootApplication
public class WxMpDemoApplication implements CommandLineRunner {

    private final Environment env;
    private final RedisConnectionFactory redisConnectionFactory;

    public WxMpDemoApplication(Environment env, RedisConnectionFactory redisConnectionFactory) {
        this.env = env;
        this.redisConnectionFactory = redisConnectionFactory;
    }

    public static void main(String[] args) {
        SpringApplication.run(WxMpDemoApplication.class, args);
    }

    public static String getCurrentRunningIp() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface ni = networkInterfaces.nextElement();
                Enumeration<InetAddress> nias = ni.getInetAddresses();
                while (nias.hasMoreElements()) {
                    InetAddress ia = nias.nextElement();
                    if (!ia.isLinkLocalAddress() && !ia.isLoopbackAddress() && ia instanceof Inet4Address) {
                        return ia.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            log.error("Unable to get local IP: " + e.getMessage(), e);
            try {
                return InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException u) {
                log.error("Unable to determine host name: " + u.getMessage(), u);
            }
        }
        return "127.0.0.1";
    }

    @Override
    public void run(String... args) {
        try {
            String ip = getCurrentRunningIp();
            String port = env.getProperty("server.port", "8080");
            String path = env.getProperty("server.servlet.context-path", "");
            if (path.equals("/")) {
                path = "";
            }

            String swaggerUiPath = env.getProperty("springdoc.swagger-ui.path", "/swagger-ui.html");

            LettuceConnectionFactory lettuceConnectionFactory = (LettuceConnectionFactory) redisConnectionFactory;
            String redisHost = lettuceConnectionFactory.getHostName();
            int redisPort = lettuceConnectionFactory.getPort();
            int redisDatabase = lettuceConnectionFactory.getDatabase();

            System.out.println("\n--------------------------------------------------------------------\n\t" +
                "Application is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
                "Swagger UI: \thttp://" + ip + ":" + port + path + swaggerUiPath + "\n\t" +
                "Redis: \t\tredis://" + redisHost + ":" + redisPort + "/" + redisDatabase + "\n\t" +
                "Profiles: \t" + Arrays.toString(env.getActiveProfiles()) + "\n" +
                "--------------------------------------------------------------------");
        } catch (Exception e) {
            // ignore
        }
    }
}
