package com.github.binarywang.demo.wx.mp.controller;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/wx/{appid}/user")
@Tag(name = "微信用户管理", description = "微信用户管理接口")
public class WxUserController {
    private final WxMpService wxService;

    @Operation(summary = "获取关注用户列表", description = "获取账号的关注者列表", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/usermanage/userinfo/api_getfans.html"))
    @GetMapping("/fans")
    public WxMpUserList getFans(@PathVariable String appid, @RequestParam(required = false) String nextOpenid)
            throws WxErrorException {
        return this.wxService.getUserService().userList(nextOpenid);
    }

    @Operation(summary = "获取用户基本信息", description = "根据 OpenID 获取用户基本信息", externalDocs = @ExternalDocumentation(description = "微信官方文档", url = "https://developers.weixin.qq.com/doc/service/api/usermanage/userinfo/api_userinfo.html"))
    @GetMapping("/userInfo")
    public WxMpUser getUserInfo(@PathVariable String appid, @RequestParam String openid) throws WxErrorException {
        return this.wxService.getUserService().userInfo(openid);
    }
}
